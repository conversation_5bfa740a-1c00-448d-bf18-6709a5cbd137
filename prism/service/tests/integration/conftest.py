import asyncio
import shutil
from subprocess import Popen, PIPE #nosec B404
import logging

import aioboto3
import grpc
import pytest
from botocore.exceptions import ClientError
#from pytest_asyncio import is_async_test # No longer needed
from testcontainers.redis import RedisContainer
from testcontainers.minio import MinioContainer
from redis.asyncio import Redis

from mlutils.utils import get_free_tcp_port
from gametime_protos.mlp.prism.v1.service_pb2_grpc import (
    FamiliesServiceStub,
    OfflineFeaturesServiceStub,
    OnlineFeaturesServiceStub,
)
from src.config import config as service_config
from src.workflow_sandbox_config import TEST_WORKFLOW_RUNNER


logging.getLogger("testcontainers").setLevel(logging.WARNING)
logging.getLogger("docker").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
logging.getLogger("aiobotocore").setLevel(logging.WARNING)
logging.getLogger("boto3").setLevel(logging.WARNING)
logging.getLogger("botocore").setLevel(logging.WARNING)


# Removed pytest_collection_modifyitems hook as pytest.ini handles scope

@pytest.fixture(scope="session")
def session_tmp_path(tmp_path_factory):
    path = tmp_path_factory.mktemp("data_session")
    print(f"\nSession temp path: {path}")
    return path


@pytest.fixture(scope="session")
async def temporal():
    host = "localhost"
    port = get_free_tcp_port()
    ui_port = get_free_tcp_port()
    print(
        f"\nAttempting to start Temporal dev server on port {port} with UI on port {ui_port}..."
    )
    temporal_path = shutil.which("temporal")

    if not temporal_path:
        print("Temporal executable not found in PATH. Using mock implementation.")
        service_config.temporal.host = host
        service_config.temporal.port = port
        temporal_url = f"{host}:{port}"
        from unittest.mock import AsyncMock, MagicMock, patch

        mock_temporal_client = MagicMock()
        mock_temporal_client.connect = AsyncMock(return_value=mock_temporal_client)
        mock_temporal_client.get_workflow_handle = MagicMock()
        mock_workflow_handle = MagicMock()
        mock_workflow_handle.result = AsyncMock(return_value=None)
        mock_workflow_handle.fetch_history = AsyncMock(return_value=MagicMock())
        mock_temporal_client.get_workflow_handle.return_value = mock_workflow_handle
        with patch("temporalio.client.Client", return_value=mock_temporal_client):
            from temporalio.client import Client as TemporalClient

            TemporalClient.connect = AsyncMock(return_value=mock_temporal_client)
            print(f"Mock Temporal client created for {temporal_url}")
            yield temporal_url
        return

    cmd = [
        temporal_path,
        "server",
        "start-dev",
        "--dynamic-config-value",
        "frontend.enableUpdateWorkflowExecution=true",
        "-p",
        str(port),
        "--ui-port",
        str(ui_port),
        "--log-level",
        "error",
    ]
    proc = None
    try:
        proc = Popen(cmd, stdout=PIPE, stderr=PIPE, text=True) #nosec B603
        await asyncio.sleep(2) 
        if proc.poll() is not None:
            stdout_output = proc.stdout.read() if proc.stdout else ""
            stderr_output = proc.stderr.read() if proc.stderr else ""
            raise RuntimeError(
                f"Temporal server failed to start. Exit code: {proc.poll()}. Stderr: {stderr_output[-500:]}. Stdout: {stdout_output[-500:]}"
            )

        service_config.temporal.host = host
        service_config.temporal.port = port
        temporal_url = f"{host}:{port}"
        print(
            f"Temporal server assumed started at {temporal_url}. Checking connection..."
        )
        try:
            from temporalio.client import Client as TemporalClient

            await TemporalClient.connect(temporal_url)
            print(f"Temporal connection successful to {temporal_url}")
            yield temporal_url
        except Exception as e:
            raise RuntimeError(f"Failed to connect to Temporal at {temporal_url}: {e}")
    finally:
        if proc and proc.poll() is None:
            print(f"\nKilling Temporal server process (PID {proc.pid})...")
            proc.terminate()
            try:
                proc.wait(timeout=5)
            except TimeoutError:
                print("Temporal did not terminate gracefully, killing...")
                proc.kill()
            print("Temporal server stopped.")


@pytest.fixture(scope="session")
async def redis():
    print("\nStarting Redis container...")
    try:
        with RedisContainer(image="redislabs/redismod:latest") as redis_container:
            host = redis_container.get_container_host_ip()
            port = redis_container.get_exposed_port(redis_container.port)
            print(f"Redis container started at {host}:{port}")
            redis_conn = Redis(host=host, port=port, decode_responses=True)
            service_config.redis.host = host
            service_config.redis.port = port
            print("Redis connection established and service config updated.")
            yield redis_conn
            print("\nClosing Redis connection...")
            await redis_conn.aclose()
            print("Redis connection closed.")
    except Exception as e:
        pytest.fail(f"Failed to start Redis container: {e}")
    finally:
        print("Redis container stopped.")


@pytest.fixture(scope="session")
async def s3():
    print("\nStarting Minio container...")
    try:
        with MinioContainer(image="minio/minio:latest") as minio:
            minio_config = minio.get_config()
            endpoint_url = f"http://{minio_config['endpoint']}"
            access_key = minio_config["access_key"]
            secret_key = minio_config["secret_key"]
            print(f"Minio container started at {endpoint_url}")
            service_config.s3.endpoint_url = endpoint_url
            service_config.s3.aws_access_key_id = access_key
            service_config.s3.aws_secret_access_key = secret_key
            bucket_name = service_config.s3.bucket
            print(f"S3 service config updated. Bucket: {bucket_name}")
            session = aioboto3.Session()
            async with session.client("s3", **service_config.s3.settings) as s3_client:
                try:
                    await s3_client.head_bucket(Bucket=bucket_name)
                    print(f"S3 bucket '{bucket_name}' already exists.")
                except ClientError as e:
                    if e.response["Error"]["Code"] in (
                        "404",
                        "NoSuchBucket",
                        "NotFound",
                    ):
                        print(f"Creating S3 bucket '{bucket_name}'...")
                        await s3_client.create_bucket(Bucket=bucket_name)
                        print(f"S3 bucket '{bucket_name}' created.")
                    else:
                        raise
                yield s3_client
    except Exception as e:
        pytest.fail(f"Failed to start Minio container: {e}")
    finally:
        print("Minio container stopped.")


@pytest.fixture(scope="session")
async def temporal_worker(temporal, s3, redis):
    print("\nInitializing Temporal worker fixture...")
    print(f"Worker using Temporal: {service_config.temporal.url}")
    print(
        f"Worker using Redis: {service_config.redis.host}:{service_config.redis.port}"
    )
    print(
        f"Worker using S3 Endpoint: {service_config.s3.endpoint_url}, Bucket: {service_config.s3.bucket}"
    )
    print(
        f"Worker using Batch Source: {service_config.source.batch.kind}, URL: {service_config.source.batch.url}"
    )
    temporal_path = shutil.which("temporal")

    if not temporal_path:
        print(
            "Temporal executable not found in PATH. Using mock worker implementation."
        )
        from unittest.mock import AsyncMock, MagicMock

        mock_worker = MagicMock()
        mock_worker.run = AsyncMock()
        mock_worker.shutdown = AsyncMock()
        print("Mock Temporal worker created.")
        yield mock_worker
        print("\nShutting down mock Temporal worker fixture...")
        return

    from src.worker import create_worker

    worker = None
    task = None
    on_shutdown_handlers = []
    try:
        print("Creating worker (using TEST_WORKFLOW_RUNNER)...")
        worker, on_shutdown_handlers = await create_worker(runner=TEST_WORKFLOW_RUNNER)
        print("Worker created. Starting worker run task...")
        task = asyncio.create_task(worker.run())
        await asyncio.sleep(1) 
        print("Temporal worker task started.")
        yield worker
    except Exception as e:
        pytest.fail(f"Temporal worker failed to start: {e}")
    finally:
        print("\nShutting down Temporal worker fixture...")
        if task and not task.done():
            print("Cancelling worker run task...")
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                print("Worker run task cancelled.")
            except Exception as e:
                print(f"Error during worker task cancellation/await: {e}")
        if worker:
            print("Shutting down worker...")
            await worker.shutdown()
            print("Worker shutdown complete.")
        if on_shutdown_handlers:
            print("Running worker shutdown handlers...")
            for handler in on_shutdown_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler()
                    else:
                        handler()
                except Exception as shutdown_e:
                    print(
                        f"Error during shutdown handler {getattr(handler, '__name__', repr(handler))}: {shutdown_e}"
                    )
            print("Worker shutdown handlers complete.")
        print("Temporal worker fixture shutdown finished.")


@pytest.fixture(scope="session")
async def service(redis, temporal, s3, temporal_worker):
    print("\nInitializing gRPC service fixture...")
    from src.shared import setup_logging

    setup_logging()

    from mlutils.grpc import Service
    from src.servicers.families import (
        Servicer as FamiliesServicer,
        register_servicer as register_families_servicer,
    )
    from src.servicers.offline_features import (
        Servicer as OfflineFeaturesServicer,
        register_servicer as register_offline_features_servicer,
    )
    from src.servicers.online_features import (
        Servicer as OnlineFeaturesServicer,
        register_servicer as register_online_features_servicer,
    )
    from src.families import Registry
    from src.stores import OnlineStore, OfflineStore
    from src.temporal import ClientWrapper

    redis_bytes = Redis(
        host=service_config.redis.host,
        port=service_config.redis.port,
        decode_responses=False,
    )
    await redis_bytes.ping()

    registry = Registry(redis)
    online_store = OnlineStore(redis_bytes)
    offline_store = OfflineStore(
        bucket_name=service_config.s3.bucket, s3_config=service_config.s3.settings
    )
    temporal_wrapper = ClientWrapper(service_config.temporal.url)
    await temporal_wrapper.connect()

    families_servicer = FamiliesServicer(registry, temporal_wrapper)
    offline_features_servicer = OfflineFeaturesServicer(
        registry, online_store, offline_store
    )
    online_features_servicer = OnlineFeaturesServicer(
        registry, online_store, offline_store
    )

    servicers = [
        (families_servicer, register_families_servicer),
        (offline_features_servicer, register_offline_features_servicer),
        (online_features_servicer, register_online_features_servicer),
    ]
    main_grpc_service = Service(servicers, on_startup=[], on_shutdown=[])

    host = "localhost"
    port = get_free_tcp_port()
    service_addr = f"{host}:{port}"
    print(f"Attempting to start gRPC service on {service_addr}...")

    server = grpc.aio.server()
    for servicer, register_fn in servicers:
        register_fn(servicer, server)
    server.add_insecure_port(service_addr)

    server_task = asyncio.create_task(server.start())
    main_grpc_service._server = server

    await asyncio.sleep(1) 
    print(f"gRPC service assumed started on {service_addr}.")

    yield service_addr

    print("\nShutting down gRPC service fixture...")
    if hasattr(main_grpc_service, "_server") and main_grpc_service._server:
        print("Stopping gRPC server...")
        await main_grpc_service._server.stop(grace=1)
        print("gRPC server stopped.")
    if server_task and not server_task.done():
        print("Waiting for server task to complete...")
        try:
            await asyncio.wait_for(server_task, timeout=2.0)
        except asyncio.TimeoutError:
            print("Server task did not complete after stop.")
        except asyncio.CancelledError:
            print("Server task cancelled.")
        except Exception as e:
            print(f"Error waiting for server task: {e}")

    await redis_bytes.aclose()
    print("gRPC service fixture shutdown finished.")


@pytest.fixture(scope="session")
async def channel(service):
    service_addr = service
    print(f"Creating gRPC channel to {service_addr}...")
    options = [
        ("grpc.enable_retries", 0),
        ("grpc.keepalive_time_ms", 10000),
        ("grpc.keepalive_timeout_ms", 5000),
        ("grpc.keepalive_permit_without_calls", 1),
        ("grpc.http2.max_pings_without_data", 0),
    ]
    async with grpc.aio.insecure_channel(service_addr, options=options) as ch:
        try:
            await asyncio.wait_for(ch.channel_ready(), timeout=10.0)
            print("gRPC channel ready.")
            yield ch
        except asyncio.TimeoutError:
            pytest.fail(
                f"Timeout waiting for gRPC channel to connect to {service_addr}"
            )
        finally:
            print("gRPC channel closing.")


@pytest.fixture(scope="session")
async def registry(redis):
    from src.families import Registry

    return Registry(redis)


@pytest.fixture(scope="session")
async def families_stub(channel):
    return FamiliesServiceStub(channel)


@pytest.fixture(scope="session")
async def offline_features_stub(channel):
    return OfflineFeaturesServiceStub(channel)


@pytest.fixture(scope="session")
async def online_features_stub(channel):
    return OnlineFeaturesServiceStub(channel)